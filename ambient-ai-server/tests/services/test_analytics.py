#!/usr/bin/env python3
"""
Tests for the analytics service.
"""

import pytest
import time
from ambient_ai_server.services.analytics import AnalyticsTracker


@pytest.fixture
def analytics_tracker():
    """Create an AnalyticsTracker for testing."""
    return AnalyticsTracker()


def test_record_chunk(analytics_tracker):
    """Test recording chunks for analytics."""
    # Record some chunks
    analytics_tracker.record_chunk("AUDIO")
    analytics_tracker.record_chunk("VIDEO_FRONT")
    analytics_tracker.record_chunk("AUDIO")
    
    stats = analytics_tracker.get_stats()
    
    assert stats["chunks"]["total_received"] == 3
    assert stats["chunks"]["by_modality"]["AUDIO"] == 2
    assert stats["chunks"]["by_modality"]["VIDEO_FRONT"] == 1


def test_session_tracking(analytics_tracker):
    """Test session tracking functionality."""
    # Start some sessions
    analytics_tracker.start_session("session1")
    analytics_tracker.start_session("session2")
    
    stats = analytics_tracker.get_stats()
    
    assert stats["sessions"]["active_count"] == 2
    assert stats["sessions"]["total_count"] == 2
    
    # End a session
    analytics_tracker.end_session("session1")
    
    stats = analytics_tracker.get_stats()
    assert stats["sessions"]["active_count"] == 1
    assert stats["sessions"]["total_count"] == 2


def test_processing_time_tracking(analytics_tracker):
    """Test processing time tracking."""
    # Record chunks with processing times
    analytics_tracker.record_chunk("AUDIO", processing_time_ms=10.5)
    analytics_tracker.record_chunk("VIDEO_FRONT", processing_time_ms=15.2)
    
    stats = analytics_tracker.get_stats()
    
    assert "performance" in stats
    assert stats["performance"]["avg_processing_time_ms"] > 0
