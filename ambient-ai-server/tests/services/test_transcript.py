#!/usr/bin/env python3
"""
Tests for the transcript service.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from ambient_ai_server.services.transcript import TranscriptManager


@pytest.fixture
def temp_recordings_dir():
    """Create a temporary directory for test recordings."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def transcript_manager(temp_recordings_dir):
    """Create a TranscriptManager with a temporary recordings directory."""
    return TranscriptManager(recordings_dir=temp_recordings_dir)


def test_create_session(transcript_manager):
    """Test creating a new session."""
    session_id = transcript_manager.create_session("test_client")
    assert session_id is not None
    assert session_id in transcript_manager.active_sessions
    
    session = transcript_manager.active_sessions[session_id]
    assert session["client_id"] == "test_client"
    assert session["chunk_count"] == 0
    assert len(session["chunks"]) == 0


def test_get_sessions(transcript_manager):
    """Test getting session information."""
    # Create a test session
    session_id = transcript_manager.create_session("test_client")
    
    sessions_info = transcript_manager.get_sessions()
    assert "active_sessions" in sessions_info
    assert "total_sessions" in sessions_info
    assert sessions_info["total_sessions"] == 1
    assert len(sessions_info["active_sessions"]) == 1
