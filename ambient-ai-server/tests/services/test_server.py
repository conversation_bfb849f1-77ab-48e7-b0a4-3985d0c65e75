#!/usr/bin/env python3
"""
Tests for the server service.
"""

import pytest
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from fastapi.testclient import TestClient
from ambient_ai_server.services.server import create_app


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    app = create_app()
    return TestClient(app)


def test_health_check(client):
    """Test the health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "version" in data


def test_analytics_endpoint(client):
    """Test the analytics endpoint."""
    response = client.get("/analytics")
    assert response.status_code == 200
    data = response.json()
    assert "chunks" in data
    assert "server_info" in data


def test_sessions_endpoint(client):
    """Test the sessions endpoint."""
    response = client.get("/sessions")
    assert response.status_code == 200
    data = response.json()
    assert "active_sessions" in data
    assert "total_sessions" in data
