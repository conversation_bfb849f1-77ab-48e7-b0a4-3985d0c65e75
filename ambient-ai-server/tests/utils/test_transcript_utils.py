#!/usr/bin/env python3
"""
Quick test of the transcript protobuf functionality.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from ambient_ai_server.utils.transcript_utils import TranscriptHandler
import time


def main():
    """Test basic transcript functionality."""
    
    print("🔄 Testing TranscriptHandler...")
    
    # Initialize handler
    handler = TranscriptHandler()
    print("✅ TranscriptHandler initialized successfully!")
    
    # Test creating an audio chunk
    now = int(time.time() * 1000)
    audio_chunk = handler.create_audio_chunk(
        pcm_data=b"test_audio_data",
        client_start_time=now - 200,
        client_end_time=now
    )
    print(f"✅ Created audio chunk: {len(audio_chunk)} bytes")
    
    # Test parsing the chunk back
    parsed = handler.parse_chunk(audio_chunk)
    print(f"✅ Parsed chunk: type={parsed['type']}, pcm_size={parsed['payload']['pcm_size']}")
    
    print("\n🎉 All tests passed! Your protobuf setup is working correctly.")


if __name__ == "__main__":
    main() 