"""
Utilities for working with transcript protobuf data.

This module provides high-level functions for serializing and deserializing
transcript data using the generated protobuf classes.
"""

from typing import Union, Dict, Any
import json
from datetime import datetime

try:
    from .protos import transcript_pb2
except ImportError:
    print("Warning: Protobuf files not found. Run 'python generate_protos.py' first.")
    transcript_pb2 = None


class TranscriptHandler:
    """High-level handler for transcript protobuf data."""
    
    def __init__(self):
        if transcript_pb2 is None:
            raise ImportError(
                "Protobuf files not generated. Run 'python generate_protos.py' first."
            )
    
    def create_audio_chunk(
        self, 
        pcm_data: bytes, 
        client_start_time: int, 
        client_end_time: int,
        server_timestamp: int = None
    ) -> bytes:
        """
        Create a serialized audio transcript chunk.
        
        Args:
            pcm_data: Raw PCM audio data (16-bit LE, 48kHz mono)
            client_start_time: Client timestamp for first sample (UNIX ms)
            client_end_time: Client timestamp for last sample (UNIX ms)
            server_timestamp: Server timestamp (UNIX ms), defaults to now
            
        Returns:
            Serialized TranscriptChunk bytes
        """
        if server_timestamp is None:
            server_timestamp = int(datetime.now().timestamp() * 1000)
        
        # Create audio data payload
        audio_data = transcript_pb2.AudioData(pcm=pcm_data)
        
        # Create transcript chunk
        chunk = transcript_pb2.TranscriptChunk(
            type=transcript_pb2.ChunkType.AUDIO,
            timestamp=server_timestamp,
            client_timestamp_start=client_start_time,
            client_timestamp_end=client_end_time,
            payload=audio_data.SerializeToString()
        )
        
        return chunk.SerializeToString()
    
    def create_video_chunk(
        self, 
        jpeg_data: bytes, 
        camera_type: str,
        client_start_time: int, 
        client_end_time: int,
        server_timestamp: int = None
    ) -> bytes:
        """
        Create a serialized video transcript chunk.
        
        Args:
            jpeg_data: JPEG encoded video frame
            camera_type: "front" or "ultrawide"
            client_start_time: Client timestamp (UNIX ms)
            client_end_time: Client timestamp (UNIX ms) 
            server_timestamp: Server timestamp (UNIX ms), defaults to now
            
        Returns:
            Serialized TranscriptChunk bytes
        """
        if server_timestamp is None:
            server_timestamp = int(datetime.now().timestamp() * 1000)
        
        # Determine chunk type
        chunk_type = (
            transcript_pb2.ChunkType.VIDEO_FRONT 
            if camera_type == "front" 
            else transcript_pb2.ChunkType.VIDEO_ULTRAWIDE
        )
        
        # Create video frame payload
        video_frame = transcript_pb2.VideoFrame(jpeg=jpeg_data)
        
        # Create transcript chunk
        chunk = transcript_pb2.TranscriptChunk(
            type=chunk_type,
            timestamp=server_timestamp,
            client_timestamp_start=client_start_time,
            client_timestamp_end=client_end_time,
            payload=video_frame.SerializeToString()
        )
        
        return chunk.SerializeToString()
    
    def create_imu_chunk(
        self,
        accel_x: float, accel_y: float, accel_z: float,
        gyro_x: float, gyro_y: float, gyro_z: float,
        gravity_x: float, gravity_y: float, gravity_z: float,
        client_start_time: int,
        client_end_time: int,
        server_timestamp: int = None
    ) -> bytes:
        """
        Create a serialized IMU transcript chunk.
        
        Args:
            accel_*: Acceleration in m/s²
            gyro_*: Angular velocity in rad/s
            gravity_*: Gravity vector in m/s²
            client_start_time: Client timestamp (UNIX ms)
            client_end_time: Client timestamp (UNIX ms)
            server_timestamp: Server timestamp (UNIX ms), defaults to now
            
        Returns:
            Serialized TranscriptChunk bytes
        """
        if server_timestamp is None:
            server_timestamp = int(datetime.now().timestamp() * 1000)
        
        # Create IMU data payload
        imu_data = transcript_pb2.IMUData(
            accel_x=accel_x, accel_y=accel_y, accel_z=accel_z,
            gyro_x=gyro_x, gyro_y=gyro_y, gyro_z=gyro_z,
            gravity_x=gravity_x, gravity_y=gravity_y, gravity_z=gravity_z
        )
        
        # Create transcript chunk
        chunk = transcript_pb2.TranscriptChunk(
            type=transcript_pb2.ChunkType.IMU,
            timestamp=server_timestamp,
            client_timestamp_start=client_start_time,
            client_timestamp_end=client_end_time,
            payload=imu_data.SerializeToString()
        )
        
        return chunk.SerializeToString()
    
    def create_location_chunk(
        self,
        latitude: float,
        longitude: float, 
        altitude: float,
        speed: float,
        accuracy: float,
        gps_timestamp: int,
        client_start_time: int,
        client_end_time: int,
        server_timestamp: int = None
    ) -> bytes:
        """
        Create a serialized location transcript chunk.
        
        Returns:
            Serialized TranscriptChunk bytes
        """
        if server_timestamp is None:
            server_timestamp = int(datetime.now().timestamp() * 1000)
        
        # Create location data payload
        location_data = transcript_pb2.LocationData(
            latitude=latitude,
            longitude=longitude,
            altitude=altitude,
            speed=speed,
            accuracy=accuracy,
            timestamp=gps_timestamp
        )
        
        # Create transcript chunk
        chunk = transcript_pb2.TranscriptChunk(
            type=transcript_pb2.ChunkType.LOCATION,
            timestamp=server_timestamp,
            client_timestamp_start=client_start_time,
            client_timestamp_end=client_end_time,
            payload=location_data.SerializeToString()
        )
        
        return chunk.SerializeToString()
    
    def create_text_chunk(
        self,
        message: str,
        client_start_time: int,
        client_end_time: int,
        server_timestamp: int = None
    ) -> bytes:
        """
        Create a serialized text transcript chunk.
        
        Returns:
            Serialized TranscriptChunk bytes
        """
        if server_timestamp is None:
            server_timestamp = int(datetime.now().timestamp() * 1000)
        
        # Create text data payload
        text_data = transcript_pb2.TextData(message=message)
        
        # Create transcript chunk
        chunk = transcript_pb2.TranscriptChunk(
            type=transcript_pb2.ChunkType.TEXT,
            timestamp=server_timestamp,
            client_timestamp_start=client_start_time,
            client_timestamp_end=client_end_time,
            payload=text_data.SerializeToString()
        )
        
        return chunk.SerializeToString()
    
    def parse_chunk(self, chunk_data: bytes) -> Dict[str, Any]:
        """
        Parse a serialized transcript chunk.
        
        Args:
            chunk_data: Serialized TranscriptChunk bytes
            
        Returns:
            Dictionary with parsed chunk data including type and payload
        """
        chunk = transcript_pb2.TranscriptChunk()
        chunk.ParseFromString(chunk_data)
        
        result = {
            "type": transcript_pb2.ChunkType.Name(chunk.type),
            "timestamp": chunk.timestamp,
            "client_timestamp_start": chunk.client_timestamp_start,
            "client_timestamp_end": chunk.client_timestamp_end,
        }
        
        # Parse payload based on type
        if chunk.type == transcript_pb2.ChunkType.AUDIO:
            audio_data = transcript_pb2.AudioData()
            audio_data.ParseFromString(chunk.payload)
            result["payload"] = {"pcm_size": len(audio_data.pcm)}
            
        elif chunk.type in [transcript_pb2.ChunkType.VIDEO_FRONT, transcript_pb2.ChunkType.VIDEO_ULTRAWIDE]:
            video_frame = transcript_pb2.VideoFrame()
            video_frame.ParseFromString(chunk.payload)
            result["payload"] = {"jpeg_size": len(video_frame.jpeg)}
            
        elif chunk.type == transcript_pb2.ChunkType.IMU:
            imu_data = transcript_pb2.IMUData()
            imu_data.ParseFromString(chunk.payload)
            result["payload"] = {
                "accel": [imu_data.accel_x, imu_data.accel_y, imu_data.accel_z],
                "gyro": [imu_data.gyro_x, imu_data.gyro_y, imu_data.gyro_z],
                "gravity": [imu_data.gravity_x, imu_data.gravity_y, imu_data.gravity_z]
            }
            
        elif chunk.type == transcript_pb2.ChunkType.LOCATION:
            location_data = transcript_pb2.LocationData()
            location_data.ParseFromString(chunk.payload)
            result["payload"] = {
                "latitude": location_data.latitude,
                "longitude": location_data.longitude,
                "altitude": location_data.altitude,
                "speed": location_data.speed,
                "accuracy": location_data.accuracy,
                "gps_timestamp": location_data.timestamp
            }
            
        elif chunk.type == transcript_pb2.ChunkType.TEXT:
            text_data = transcript_pb2.TextData()
            text_data.ParseFromString(chunk.payload)
            result["payload"] = {"message": text_data.message}
        
        return result 