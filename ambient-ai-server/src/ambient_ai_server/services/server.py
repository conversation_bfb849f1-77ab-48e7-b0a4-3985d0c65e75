"""
FastAPI server implementation for the Ambient AI Server.

This module provides the main FastAPI application with WebSocket endpoints
for receiving real-time multimodal sensor data.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.responses import JSONResponse

from .transcript import TranscriptManager
from .analytics import AnalyticsTracker
from ..utils.transcript_utils import TranscriptHandler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages WebSocket connections."""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"Client {client_id} connected")
    
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection."""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"Client {client_id} disconnected")
    
    async def send_message(self, client_id: str, message: str):
        """Send a message to a specific client."""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            await websocket.send_text(message)


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    
    app = FastAPI(
        title="Ambient AI Server",
        description="Real-time multimodal ingest system for ambient AI data",
        version="0.1.0"
    )
    
    # Initialize components
    connection_manager = ConnectionManager()
    transcript_manager = TranscriptManager()
    analytics_tracker = AnalyticsTracker()
    transcript_handler = TranscriptHandler()
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "0.1.0"
        }
    
    @app.get("/analytics")
    async def get_analytics():
        """Get current analytics data."""
        return analytics_tracker.get_stats()
    
    @app.websocket("/ws")
    async def websocket_endpoint(websocket: WebSocket):
        """Main WebSocket endpoint for receiving transcript chunks."""
        
        # Generate a unique client ID
        client_id = f"client_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            await connection_manager.connect(websocket, client_id)
            
            # Start a new session
            session_id = transcript_manager.start_session()
            logger.info(f"Started session {session_id} for client {client_id}")
            
            # Send welcome message
            await websocket.send_text(f"Connected to Ambient AI Server. Session: {session_id}")
            
            while True:
                # Receive binary data (protobuf TranscriptChunk)
                data = await websocket.receive_bytes()
                
                try:
                    # Parse the chunk to validate it
                    parsed_chunk = transcript_handler.parse_chunk(data)
                    
                    # Add server timestamp
                    server_timestamp = int(datetime.now().timestamp() * 1000)
                    
                    # Store the chunk
                    await transcript_manager.add_chunk(session_id, data, server_timestamp)
                    
                    # Update analytics
                    analytics_tracker.record_chunk(parsed_chunk['type'])
                    
                    # Log the received chunk
                    logger.info(
                        f"Received {parsed_chunk['type']} chunk from {client_id} "
                        f"(session: {session_id})"
                    )
                    
                except Exception as e:
                    logger.error(f"Error processing chunk from {client_id}: {e}")
                    await websocket.send_text(f"Error processing chunk: {str(e)}")
                
        except WebSocketDisconnect:
            logger.info(f"Client {client_id} disconnected")
        except Exception as e:
            logger.error(f"WebSocket error for client {client_id}: {e}")
        finally:
            connection_manager.disconnect(client_id)
            if 'session_id' in locals():
                await transcript_manager.end_session(session_id)
                logger.info(f"Ended session {session_id}")
    
    @app.post("/sessions/{session_id}/end")
    async def end_session(session_id: str):
        """Manually end a session."""
        try:
            await transcript_manager.end_session(session_id)
            return {"message": f"Session {session_id} ended successfully"}
        except Exception as e:
            raise HTTPException(status_code=404, detail=str(e))
    
    @app.get("/sessions")
    async def list_sessions():
        """List all recorded sessions."""
        return transcript_manager.list_sessions()
    
    return app
