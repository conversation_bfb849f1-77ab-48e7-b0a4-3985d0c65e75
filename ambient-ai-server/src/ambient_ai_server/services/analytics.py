"""
Analytics tracking system for monitoring server performance and data flow.

This module provides real-time analytics for tracking chunk counts,
modality frequencies, and system performance metrics.
"""

import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, Any, List
import threading


class AnalyticsTracker:
    """Tracks analytics for the Ambient AI Server."""
    
    def __init__(self, history_window_minutes: int = 60):
        """
        Initialize the analytics tracker.
        
        Args:
            history_window_minutes: How long to keep detailed history (in minutes)
        """
        self.history_window = timedelta(minutes=history_window_minutes)
        self.start_time = datetime.now()
        
        # Thread-safe counters
        self._lock = threading.Lock()
        
        # Total counters
        self.total_chunks = 0
        self.modality_counts = defaultdict(int)
        
        # Time-based tracking
        self.chunk_history = deque()  # (timestamp, chunk_type)
        self.last_activity = None
        
        # Performance metrics
        self.processing_times = deque(maxlen=1000)  # Last 1000 processing times
        
        # Session tracking
        self.active_sessions = set()
        self.total_sessions = 0
    
    def record_chunk(self, chunk_type: str, processing_time_ms: float = None):
        """
        Record a received chunk for analytics.
        
        Args:
            chunk_type: Type of the chunk (e.g., 'AUDIO', 'VIDEO_FRONT')
            processing_time_ms: Time taken to process the chunk (optional)
        """
        with self._lock:
            now = datetime.now()
            
            # Update counters
            self.total_chunks += 1
            self.modality_counts[chunk_type] += 1
            self.last_activity = now
            
            # Add to history
            self.chunk_history.append((now, chunk_type))
            
            # Record processing time if provided
            if processing_time_ms is not None:
                self.processing_times.append(processing_time_ms)
            
            # Clean old history
            self._clean_old_history()
    
    def start_session(self, session_id: str):
        """Record the start of a new session."""
        with self._lock:
            self.active_sessions.add(session_id)
            self.total_sessions += 1
    
    def end_session(self, session_id: str):
        """Record the end of a session."""
        with self._lock:
            self.active_sessions.discard(session_id)
    
    def _clean_old_history(self):
        """Remove old entries from chunk history."""
        cutoff_time = datetime.now() - self.history_window
        
        while self.chunk_history and self.chunk_history[0][0] < cutoff_time:
            self.chunk_history.popleft()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get current analytics statistics.
        
        Returns:
            Dictionary with comprehensive analytics data
        """
        with self._lock:
            now = datetime.now()
            uptime = now - self.start_time
            
            # Calculate recent activity (last hour)
            recent_chunks = [
                (timestamp, chunk_type) 
                for timestamp, chunk_type in self.chunk_history
                if timestamp > now - timedelta(hours=1)
            ]
            
            # Calculate chunks per minute for recent activity
            recent_modality_counts = defaultdict(int)
            for _, chunk_type in recent_chunks:
                recent_modality_counts[chunk_type] += 1
            
            # Calculate average processing time
            avg_processing_time = (
                sum(self.processing_times) / len(self.processing_times)
                if self.processing_times else 0
            )
            
            # Calculate throughput (chunks per minute)
            minutes_elapsed = max(uptime.total_seconds() / 60, 1)
            overall_throughput = self.total_chunks / minutes_elapsed
            
            recent_minutes = min(60, minutes_elapsed)
            recent_throughput = len(recent_chunks) / recent_minutes
            
            return {
                'server_info': {
                    'uptime_seconds': int(uptime.total_seconds()),
                    'uptime_human': str(uptime).split('.')[0],  # Remove microseconds
                    'start_time': self.start_time.isoformat(),
                    'last_activity': self.last_activity.isoformat() if self.last_activity else None
                },
                'chunks': {
                    'total_received': self.total_chunks,
                    'recent_hour': len(recent_chunks),
                    'by_modality': dict(self.modality_counts),
                    'recent_by_modality': dict(recent_modality_counts)
                },
                'performance': {
                    'avg_processing_time_ms': round(avg_processing_time, 2),
                    'throughput_chunks_per_minute': round(overall_throughput, 2),
                    'recent_throughput_chunks_per_minute': round(recent_throughput, 2)
                },
                'sessions': {
                    'active_count': len(self.active_sessions),
                    'active_sessions': list(self.active_sessions),
                    'total_sessions': self.total_sessions
                },
                'timestamp': now.isoformat()
            }
    
    def get_modality_breakdown(self) -> Dict[str, Dict[str, Any]]:
        """
        Get detailed breakdown by modality.
        
        Returns:
            Dictionary with per-modality statistics
        """
        with self._lock:
            breakdown = {}
            
            for modality, count in self.modality_counts.items():
                # Calculate percentage of total
                percentage = (count / self.total_chunks * 100) if self.total_chunks > 0 else 0
                
                # Calculate recent activity for this modality
                recent_count = sum(
                    1 for _, chunk_type in self.chunk_history
                    if chunk_type == modality
                )
                
                breakdown[modality] = {
                    'total_chunks': count,
                    'percentage_of_total': round(percentage, 2),
                    'recent_hour_chunks': recent_count
                }
            
            return breakdown
    
    def get_timeline_data(self, minutes: int = 60) -> List[Dict[str, Any]]:
        """
        Get timeline data for the specified number of minutes.
        
        Args:
            minutes: Number of minutes of history to return
            
        Returns:
            List of time-bucketed chunk counts
        """
        with self._lock:
            now = datetime.now()
            cutoff_time = now - timedelta(minutes=minutes)
            
            # Filter recent chunks
            recent_chunks = [
                (timestamp, chunk_type)
                for timestamp, chunk_type in self.chunk_history
                if timestamp > cutoff_time
            ]
            
            # Create time buckets (1-minute intervals)
            buckets = []
            for i in range(minutes):
                bucket_start = cutoff_time + timedelta(minutes=i)
                bucket_end = bucket_start + timedelta(minutes=1)
                
                # Count chunks in this bucket
                bucket_chunks = [
                    chunk_type for timestamp, chunk_type in recent_chunks
                    if bucket_start <= timestamp < bucket_end
                ]
                
                modality_counts = defaultdict(int)
                for chunk_type in bucket_chunks:
                    modality_counts[chunk_type] += 1
                
                buckets.append({
                    'timestamp': bucket_start.isoformat(),
                    'total_chunks': len(bucket_chunks),
                    'modality_counts': dict(modality_counts)
                })
            
            return buckets
    
    def reset_stats(self):
        """Reset all analytics statistics."""
        with self._lock:
            self.total_chunks = 0
            self.modality_counts.clear()
            self.chunk_history.clear()
            self.processing_times.clear()
            self.last_activity = None
            self.start_time = datetime.now()
